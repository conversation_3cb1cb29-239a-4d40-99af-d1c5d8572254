# TurtleFlow Pro Environment Configuration
# Copy this file to .env and update with your actual values

# Application Settings
APP_NAME=TurtleFlow Pro
APP_VERSION=1.0.0
DEBUG=false
API_V1_STR=/api/v1

# Security
SECRET_KEY=your-super-secret-key-change-in-production-minimum-32-characters
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256

# Database Configuration
POSTGRES_SERVER=localhost
POSTGRES_USER=turtleflow
POSTGRES_PASSWORD=your-secure-password
POSTGRES_DB=turtleflow_db
POSTGRES_PORT=5432

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-redis-password

# InfluxDB Configuration (Time Series Data)
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token
INFLUXDB_ORG=turtleflow
INFLUXDB_BUCKET=market_data

# Trading Configuration
DEFAULT_RISK_PER_TRADE=0.01
MAX_POSITION_SIZE=0.25
MAX_DAILY_LOSS=0.02
MAX_DRAWDOWN=0.20

# Turtle Trading Parameters
SYSTEM_1_ENTRY_PERIOD=20
SYSTEM_1_EXIT_PERIOD=10
SYSTEM_2_ENTRY_PERIOD=55
SYSTEM_2_EXIT_PERIOD=20
ATR_PERIOD=20
ATR_MULTIPLIER=2.0
PYRAMID_ATR_MULTIPLIER=0.5

# Exchange API Keys - Binance
BINANCE_API_KEY=your-binance-api-key
BINANCE_SECRET_KEY=your-binance-secret-key
BINANCE_TESTNET=true

# Exchange API Keys - Coinbase Pro
COINBASE_API_KEY=your-coinbase-api-key
COINBASE_SECRET_KEY=your-coinbase-secret-key
COINBASE_PASSPHRASE=your-coinbase-passphrase
COINBASE_SANDBOX=true

# Exchange API Keys - Alpha Vantage (Stocks)
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-api-key

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30
WS_RECONNECT_ATTEMPTS=5
WS_RECONNECT_DELAY=5

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# CORS Origins (comma-separated)
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8080,https://turtleflowpro.netlify.app

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100

# Monitoring
PROMETHEUS_PORT=8001
HEALTH_CHECK_INTERVAL=60

# File Storage
UPLOAD_DIR=uploads
MAX_FILE_SIZE=********

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true

# SMS Configuration (Optional - Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Slack Notifications (Optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook

# AWS Configuration (for production deployment)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_S3_BUCKET=turtleflow-storage

# Google Cloud Configuration (alternative to AWS)
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# Frontend Configuration
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000
VITE_APP_NAME=TurtleFlow Pro
VITE_APP_VERSION=1.0.0

# Production URLs (update for production)
PRODUCTION_API_URL=https://api.turtleflowpro.com
PRODUCTION_WS_URL=wss://api.turtleflowpro.com
PRODUCTION_FRONTEND_URL=https://turtleflowpro.com

# SSL Configuration (for production)
SSL_CERT_PATH=/etc/ssl/certs/turtleflow.crt
SSL_KEY_PATH=/etc/ssl/private/turtleflow.key

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=turtleflow-backups

# Performance Tuning
UVICORN_WORKERS=4
UVICORN_MAX_REQUESTS=1000
UVICORN_MAX_REQUESTS_JITTER=100

# Development Settings
DEV_RELOAD=true
DEV_PORT=8000
DEV_HOST=0.0.0.0
