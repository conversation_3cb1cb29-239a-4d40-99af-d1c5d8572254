"""
TurtleFlow Pro - Market Data Service
Real-time and historical market data from multiple exchanges
"""

import asyncio
import websockets
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
import logging
import ccxt
from binance import AsyncClient as BinanceClient
import requests
from alpha_vantage.timeseries import TimeSeries

from ..core.config import settings
from ..core.database import influx_manager, redis_manager
from ..utils.indicators import TechnicalIndicators

logger = logging.getLogger(__name__)


class MarketDataService:
    """Unified market data service for multiple exchanges"""
    
    def __init__(self):
        self.exchanges = {}
        self.websocket_connections = {}
        self.subscribers = {}
        self.is_running = False
        
        # Initialize exchange clients
        self._init_exchanges()
    
    def _init_exchanges(self):
        """Initialize exchange clients"""
        try:
            # Binance
            if settings.BINANCE_API_KEY and settings.BINANCE_SECRET_KEY:
                self.exchanges['binance'] = ccxt.binance({
                    'apiKey': settings.BINANCE_API_KEY,
                    'secret': settings.BINANCE_SECRET_KEY,
                    'sandbox': settings.BINANCE_TESTNET,
                    'enableRateLimit': True,
                })
            
            # Coinbase Pro
            if settings.COINBASE_API_KEY and settings.COINBASE_SECRET_KEY:
                self.exchanges['coinbase'] = ccxt.coinbasepro({
                    'apiKey': settings.COINBASE_API_KEY,
                    'secret': settings.COINBASE_SECRET_KEY,
                    'passphrase': settings.COINBASE_PASSPHRASE,
                    'sandbox': settings.COINBASE_SANDBOX,
                    'enableRateLimit': True,
                })
            
            # Alpha Vantage for stocks
            if settings.ALPHA_VANTAGE_API_KEY:
                self.alpha_vantage = TimeSeries(
                    key=settings.ALPHA_VANTAGE_API_KEY,
                    output_format='pandas'
                )
            
            logger.info(f"Initialized {len(self.exchanges)} exchange connections")
        except Exception as e:
            logger.error(f"Error initializing exchanges: {e}")
            raise
    
    async def get_historical_data(self, symbol: str, timeframe: str = '1h', 
                                limit: int = 100, exchange: str = 'binance') -> pd.DataFrame:
        """
        Get historical OHLCV data
        """
        try:
            if exchange not in self.exchanges:
                raise ValueError(f"Exchange {exchange} not configured")
            
            # Check cache first
            cache_key = f"historical:{exchange}:{symbol}:{timeframe}:{limit}"
            cached_data = await redis_manager.get_cache(cache_key)
            
            if cached_data:
                return pd.read_json(cached_data)
            
            # Fetch from exchange
            exchange_client = self.exchanges[exchange]
            ohlcv = exchange_client.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Cache the data
            await redis_manager.set_cache(cache_key, df.to_json(), expire=300)  # 5 minutes
            
            # Store in InfluxDB
            await self._store_historical_data(symbol, df, exchange)
            
            return df
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {e}")
            raise
    
    async def get_current_price(self, symbol: str, exchange: str = 'binance') -> Dict:
        """
        Get current price for a symbol
        """
        try:
            if exchange not in self.exchanges:
                raise ValueError(f"Exchange {exchange} not configured")
            
            # Check cache first
            cache_key = f"price:{exchange}:{symbol}"
            cached_price = await redis_manager.get_cache(cache_key)
            
            if cached_price:
                return json.loads(cached_price)
            
            # Fetch from exchange
            exchange_client = self.exchanges[exchange]
            ticker = exchange_client.fetch_ticker(symbol)
            
            price_data = {
                'symbol': symbol,
                'price': ticker['last'],
                'bid': ticker['bid'],
                'ask': ticker['ask'],
                'volume': ticker['baseVolume'],
                'change': ticker['change'],
                'percentage': ticker['percentage'],
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # Cache for 10 seconds
            await redis_manager.set_cache(cache_key, json.dumps(price_data), expire=10)
            
            return price_data
        except Exception as e:
            logger.error(f"Error fetching current price for {symbol}: {e}")
            raise
    
    async def subscribe_to_price_updates(self, symbols: List[str], callback: Callable, 
                                       exchange: str = 'binance'):
        """
        Subscribe to real-time price updates via WebSocket
        """
        try:
            if exchange == 'binance':
                await self._subscribe_binance_websocket(symbols, callback)
            elif exchange == 'coinbase':
                await self._subscribe_coinbase_websocket(symbols, callback)
            else:
                raise ValueError(f"WebSocket not supported for {exchange}")
        except Exception as e:
            logger.error(f"Error subscribing to price updates: {e}")
            raise
    
    async def _subscribe_binance_websocket(self, symbols: List[str], callback: Callable):
        """
        Subscribe to Binance WebSocket streams
        """
        try:
            # Convert symbols to Binance format
            streams = [f"{symbol.lower()}@ticker" for symbol in symbols]
            stream_url = f"wss://stream.binance.com:9443/ws/{'/'.join(streams)}"
            
            async with websockets.connect(stream_url) as websocket:
                self.websocket_connections['binance'] = websocket
                logger.info(f"Connected to Binance WebSocket for {len(symbols)} symbols")
                
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        
                        # Process ticker data
                        if 'e' in data and data['e'] == '24hrTicker':
                            price_update = {
                                'symbol': data['s'],
                                'price': float(data['c']),
                                'volume': float(data['v']),
                                'change': float(data['P']),
                                'timestamp': datetime.utcnow().isoformat(),
                                'exchange': 'binance'
                            }
                            
                            # Call the callback function
                            await callback(price_update)
                            
                            # Cache the update
                            cache_key = f"price:binance:{data['s']}"
                            await redis_manager.set_cache(
                                cache_key, 
                                json.dumps(price_update), 
                                expire=60
                            )
                    except Exception as e:
                        logger.error(f"Error processing WebSocket message: {e}")
        except Exception as e:
            logger.error(f"Binance WebSocket error: {e}")
            raise
    
    async def _subscribe_coinbase_websocket(self, symbols: List[str], callback: Callable):
        """
        Subscribe to Coinbase Pro WebSocket streams
        """
        try:
            websocket_url = "wss://ws-feed.pro.coinbase.com"
            
            subscribe_message = {
                "type": "subscribe",
                "product_ids": symbols,
                "channels": ["ticker"]
            }
            
            async with websockets.connect(websocket_url) as websocket:
                self.websocket_connections['coinbase'] = websocket
                await websocket.send(json.dumps(subscribe_message))
                logger.info(f"Connected to Coinbase WebSocket for {len(symbols)} symbols")
                
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        
                        if data.get('type') == 'ticker':
                            price_update = {
                                'symbol': data['product_id'],
                                'price': float(data['price']),
                                'volume': float(data['volume_24h']),
                                'timestamp': data['time'],
                                'exchange': 'coinbase'
                            }
                            
                            await callback(price_update)
                            
                            # Cache the update
                            cache_key = f"price:coinbase:{data['product_id']}"
                            await redis_manager.set_cache(
                                cache_key,
                                json.dumps(price_update),
                                expire=60
                            )
                    except Exception as e:
                        logger.error(f"Error processing Coinbase WebSocket message: {e}")
        except Exception as e:
            logger.error(f"Coinbase WebSocket error: {e}")
            raise
    
    async def get_stock_data(self, symbol: str, interval: str = 'daily') -> pd.DataFrame:
        """
        Get stock data from Alpha Vantage
        """
        try:
            if not hasattr(self, 'alpha_vantage'):
                raise ValueError("Alpha Vantage not configured")
            
            # Check cache first
            cache_key = f"stock:{symbol}:{interval}"
            cached_data = await redis_manager.get_cache(cache_key)
            
            if cached_data:
                return pd.read_json(cached_data)
            
            # Fetch from Alpha Vantage
            if interval == 'daily':
                data, meta_data = self.alpha_vantage.get_daily(symbol, outputsize='compact')
            elif interval == 'intraday':
                data, meta_data = self.alpha_vantage.get_intraday(symbol, interval='1min')
            else:
                raise ValueError(f"Unsupported interval: {interval}")
            
            # Rename columns to match our format
            data.columns = ['open', 'high', 'low', 'close', 'volume']
            data.index.name = 'timestamp'
            
            # Cache for 1 hour
            await redis_manager.set_cache(cache_key, data.to_json(), expire=3600)
            
            return data
        except Exception as e:
            logger.error(f"Error fetching stock data for {symbol}: {e}")
            raise
    
    async def _store_historical_data(self, symbol: str, data: pd.DataFrame, exchange: str):
        """
        Store historical data in InfluxDB
        """
        try:
            for timestamp, row in data.iterrows():
                influx_manager.write_market_data(
                    symbol=f"{exchange}:{symbol}",
                    timestamp=timestamp,
                    open_price=row['open'],
                    high=row['high'],
                    low=row['low'],
                    close=row['close'],
                    volume=row['volume']
                )
        except Exception as e:
            logger.error(f"Error storing historical data: {e}")
    
    async def calculate_signals(self, symbol: str, system: int = 1, 
                              exchange: str = 'binance') -> Dict:
        """
        Calculate Turtle Trading signals for a symbol
        """
        try:
            # Get historical data
            data = await self.get_historical_data(symbol, '1h', 100, exchange)
            
            # Calculate signals
            signals = TechnicalIndicators.turtle_entry_signals(data, system)
            
            # Get latest signal
            latest_signal = signals.iloc[-1]
            current_price = data['close'].iloc[-1]
            
            signal_data = {
                'symbol': symbol,
                'system': system,
                'current_price': current_price,
                'entry_long': bool(latest_signal['entry_long']),
                'entry_short': bool(latest_signal['entry_short']),
                'exit_long': bool(latest_signal['exit_long']),
                'exit_short': bool(latest_signal['exit_short']),
                'entry_high_level': latest_signal['entry_high_level'],
                'entry_low_level': latest_signal['entry_low_level'],
                'exit_high_level': latest_signal['exit_high_level'],
                'exit_low_level': latest_signal['exit_low_level'],
                'atr': latest_signal['atr'],
                'timestamp': datetime.utcnow().isoformat()
            }
            
            return signal_data
        except Exception as e:
            logger.error(f"Error calculating signals for {symbol}: {e}")
            raise
    
    async def get_multiple_prices(self, symbols: List[str], 
                                exchange: str = 'binance') -> Dict[str, Dict]:
        """
        Get current prices for multiple symbols
        """
        try:
            prices = {}
            tasks = []
            
            for symbol in symbols:
                task = self.get_current_price(symbol, exchange)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Error fetching price for {symbols[i]}: {result}")
                    continue
                
                prices[symbols[i]] = result
            
            return prices
        except Exception as e:
            logger.error(f"Error fetching multiple prices: {e}")
            raise
    
    async def health_check(self) -> Dict[str, bool]:
        """
        Check health of all market data connections
        """
        health_status = {}
        
        for exchange_name, exchange_client in self.exchanges.items():
            try:
                # Test connection by fetching a ticker
                exchange_client.fetch_ticker('BTC/USDT')
                health_status[exchange_name] = True
            except Exception as e:
                logger.error(f"Health check failed for {exchange_name}: {e}")
                health_status[exchange_name] = False
        
        return health_status


# Global market data service instance
market_data_service = MarketDataService()
