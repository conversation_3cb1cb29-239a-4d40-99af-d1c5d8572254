"""
TurtleFlow Pro - Configuration Management
Centralized configuration for the trading bot application
"""

from typing import Optional, List
from pydantic import BaseSettings, validator
import os
from pathlib import Path


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application
    APP_NAME: str = "TurtleFlow Pro"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    API_V1_STR: str = "/api/v1"
    
    # Security
    SECRET_KEY: str = "your-super-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"
    
    # Database
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "turtleflow"
    POSTGRES_PASSWORD: str = "password"
    POSTGRES_DB: str = "turtleflow_db"
    POSTGRES_PORT: int = 5432
    
    @property
    def DATABASE_URL(self) -> str:
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
    
    # Redis
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    @property
    def REDIS_URL(self) -> str:
        auth = f":{self.REDIS_PASSWORD}@" if self.REDIS_PASSWORD else ""
        return f"redis://{auth}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    # InfluxDB (Time Series Data)
    INFLUXDB_URL: str = "http://localhost:8086"
    INFLUXDB_TOKEN: str = "your-influxdb-token"
    INFLUXDB_ORG: str = "turtleflow"
    INFLUXDB_BUCKET: str = "market_data"
    
    # Trading Configuration
    DEFAULT_RISK_PER_TRADE: float = 0.01  # 1% risk per trade
    MAX_POSITION_SIZE: float = 0.25  # 25% max position size
    MAX_DAILY_LOSS: float = 0.02  # 2% max daily loss
    MAX_DRAWDOWN: float = 0.20  # 20% max drawdown
    
    # Turtle Trading Parameters
    SYSTEM_1_ENTRY_PERIOD: int = 20
    SYSTEM_1_EXIT_PERIOD: int = 10
    SYSTEM_2_ENTRY_PERIOD: int = 55
    SYSTEM_2_EXIT_PERIOD: int = 20
    ATR_PERIOD: int = 20
    ATR_MULTIPLIER: float = 2.0
    PYRAMID_ATR_MULTIPLIER: float = 0.5
    
    # Exchange API Keys
    BINANCE_API_KEY: Optional[str] = None
    BINANCE_SECRET_KEY: Optional[str] = None
    BINANCE_TESTNET: bool = True
    
    COINBASE_API_KEY: Optional[str] = None
    COINBASE_SECRET_KEY: Optional[str] = None
    COINBASE_PASSPHRASE: Optional[str] = None
    COINBASE_SANDBOX: bool = True
    
    ALPHA_VANTAGE_API_KEY: Optional[str] = None
    
    # WebSocket Configuration
    WS_HEARTBEAT_INTERVAL: int = 30
    WS_RECONNECT_ATTEMPTS: int = 5
    WS_RECONNECT_DELAY: int = 5
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # CORS
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8080",
        "https://turtleflowpro.netlify.app"
    ]
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 100
    
    # Monitoring
    PROMETHEUS_PORT: int = 8001
    HEALTH_CHECK_INTERVAL: int = 60
    
    # File Storage
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings


# Trading pairs configuration
SUPPORTED_PAIRS = {
    "crypto": [
        "BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT",
        "BNBUSDT", "SOLUSDT", "MATICUSDT", "AVAXUSDT", "ATOMUSDT"
    ],
    "forex": [
        "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD",
        "NZDUSD", "USDCHF", "EURJPY", "GBPJPY", "EURGBP"
    ],
    "stocks": [
        "AAPL", "GOOGL", "MSFT", "AMZN", "TSLA",
        "NVDA", "META", "NFLX", "AMD", "CRM"
    ]
}

# Market hours configuration
MARKET_HOURS = {
    "crypto": {"open": "00:00", "close": "23:59", "timezone": "UTC"},
    "forex": {"open": "17:00", "close": "17:00", "timezone": "EST"},  # Sunday 5 PM to Friday 5 PM
    "stocks": {"open": "09:30", "close": "16:00", "timezone": "EST"}
}

# Risk management limits
RISK_LIMITS = {
    "max_units_per_market": 4,
    "max_units_per_sector": 10,
    "max_total_units": 12,
    "correlation_threshold": 0.7,
    "min_account_balance": 1000.0
}
