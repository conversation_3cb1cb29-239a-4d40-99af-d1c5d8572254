#!/bin/bash

# TurtleFlow Pro Setup Script
# Automated setup for development and production environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root for security reasons"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check Node.js (for local development)
    if ! command -v node &> /dev/null; then
        log_warning "Node.js is not installed. Frontend development will require Node.js 18+."
    else
        NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$NODE_VERSION" -lt 18 ]; then
            log_warning "Node.js version is $NODE_VERSION. Version 18+ is recommended."
        fi
    fi
    
    # Check Python (for local development)
    if ! command -v python3 &> /dev/null; then
        log_warning "Python 3 is not installed. Backend development will require Python 3.11+."
    else
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1-2)
        log_info "Python version: $PYTHON_VERSION"
    fi
    
    log_success "System requirements check completed"
}

# Setup environment file
setup_environment() {
    log_info "Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_success "Created .env file from .env.example"
            log_warning "Please update .env file with your actual configuration values"
        else
            log_error ".env.example file not found"
            exit 1
        fi
    else
        log_info ".env file already exists"
    fi
}

# Generate secure secrets
generate_secrets() {
    log_info "Generating secure secrets..."
    
    # Generate SECRET_KEY
    SECRET_KEY=$(openssl rand -hex 32)
    
    # Generate database passwords
    POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    REDIS_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    
    # Generate InfluxDB token
    INFLUXDB_TOKEN=$(openssl rand -hex 32)
    
    # Update .env file
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/SECRET_KEY=.*/SECRET_KEY=$SECRET_KEY/" .env
        sed -i '' "s/POSTGRES_PASSWORD=.*/POSTGRES_PASSWORD=$POSTGRES_PASSWORD/" .env
        sed -i '' "s/REDIS_PASSWORD=.*/REDIS_PASSWORD=$REDIS_PASSWORD/" .env
        sed -i '' "s/INFLUXDB_TOKEN=.*/INFLUXDB_TOKEN=$INFLUXDB_TOKEN/" .env
    else
        # Linux
        sed -i "s/SECRET_KEY=.*/SECRET_KEY=$SECRET_KEY/" .env
        sed -i "s/POSTGRES_PASSWORD=.*/POSTGRES_PASSWORD=$POSTGRES_PASSWORD/" .env
        sed -i "s/REDIS_PASSWORD=.*/REDIS_PASSWORD=$REDIS_PASSWORD/" .env
        sed -i "s/INFLUXDB_TOKEN=.*/INFLUXDB_TOKEN=$INFLUXDB_TOKEN/" .env
    fi
    
    log_success "Generated secure secrets and updated .env file"
}

# Setup directories
setup_directories() {
    log_info "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p backups
    mkdir -p ssl
    mkdir -p monitoring/prometheus
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    mkdir -p nginx
    
    log_success "Created directory structure"
}

# Install backend dependencies (for local development)
setup_backend() {
    log_info "Setting up backend dependencies..."
    
    if [ -d "backend" ]; then
        cd backend
        
        # Create virtual environment
        if [ ! -d "venv" ]; then
            python3 -m venv venv
            log_success "Created Python virtual environment"
        fi
        
        # Activate virtual environment and install dependencies
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt
        
        log_success "Installed backend dependencies"
        cd ..
    else
        log_warning "Backend directory not found"
    fi
}

# Install frontend dependencies (for local development)
setup_frontend() {
    log_info "Setting up frontend dependencies..."
    
    if [ -d "frontend" ]; then
        cd frontend
        
        if command -v npm &> /dev/null; then
            npm install
            log_success "Installed frontend dependencies"
        else
            log_warning "npm not found. Skipping frontend setup."
        fi
        
        cd ..
    else
        log_warning "Frontend directory not found"
    fi
}

# Setup Docker containers
setup_docker() {
    log_info "Setting up Docker containers..."
    
    # Pull required images
    docker-compose pull
    
    # Build custom images
    docker-compose build
    
    log_success "Docker setup completed"
}

# Initialize databases
init_databases() {
    log_info "Initializing databases..."
    
    # Start database services
    docker-compose up -d postgres redis influxdb
    
    # Wait for databases to be ready
    log_info "Waiting for databases to be ready..."
    sleep 30
    
    # Run database migrations (if applicable)
    # docker-compose exec backend alembic upgrade head
    
    log_success "Databases initialized"
}

# Setup monitoring
setup_monitoring() {
    log_info "Setting up monitoring..."
    
    # Create Prometheus configuration
    cat > monitoring/prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'turtleflow-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
EOF
    
    log_success "Monitoring configuration created"
}

# Setup SSL certificates (for production)
setup_ssl() {
    if [ "$1" = "production" ]; then
        log_info "Setting up SSL certificates for production..."
        
        # This would typically use Let's Encrypt or provided certificates
        log_warning "SSL setup requires manual configuration for production"
        log_info "Please place your SSL certificates in the ssl/ directory"
    fi
}

# Main setup function
main() {
    log_info "Starting TurtleFlow Pro setup..."
    
    # Parse command line arguments
    ENVIRONMENT=${1:-development}
    
    # Run setup steps
    check_root
    check_requirements
    setup_environment
    generate_secrets
    setup_directories
    
    if [ "$ENVIRONMENT" = "development" ]; then
        setup_backend
        setup_frontend
    fi
    
    setup_docker
    setup_monitoring
    setup_ssl "$ENVIRONMENT"
    
    if [ "$ENVIRONMENT" = "production" ]; then
        init_databases
    fi
    
    log_success "TurtleFlow Pro setup completed successfully!"
    
    # Display next steps
    echo ""
    log_info "Next steps:"
    echo "1. Update .env file with your API keys and configuration"
    echo "2. For development: docker-compose up -d"
    echo "3. For production: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d"
    echo "4. Access the application at http://localhost:3000"
    echo "5. API documentation at http://localhost:8000/docs"
    echo "6. Monitoring dashboard at http://localhost:3001 (admin/admin123)"
    echo ""
    log_warning "Remember to secure your API keys and change default passwords!"
}

# Run main function with all arguments
main "$@"
