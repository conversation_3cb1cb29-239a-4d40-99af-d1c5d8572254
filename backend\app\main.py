"""
TurtleFlow Pro - Main FastAPI Application
Entry point for the TurtleFlow Pro trading bot API
"""

from fastapi import <PERSON>AP<PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse
from contextlib import asynccontextmanager
import logging
import time
import uvicorn

from .core.config import settings
from .core.database import init_databases, close_databases, create_tables
from .core.security import SecurityHeaders, rate_limiter
from .api import auth, trading, portfolio
from .services.trading_engine import trading_engine
from .services.market_data import market_data_service

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format=settings.LOG_FORMAT
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting TurtleFlow Pro...")
    
    try:
        # Initialize databases
        await init_databases()
        
        # Create database tables
        create_tables()
        
        # Start trading engine (in background)
        if not settings.DEBUG:
            import asyncio
            asyncio.create_task(trading_engine.start())
        
        logger.info("TurtleFlow Pro started successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Startup error: {e}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down TurtleFlow Pro...")
        
        # Stop trading engine
        await trading_engine.stop()
        
        # Close database connections
        await close_databases()
        
        logger.info("TurtleFlow Pro shutdown complete")


# Create FastAPI application
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="Professional Turtle Trading Bot with Real-time Execution",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Security middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else ["turtleflowpro.com", "*.turtleflowpro.com"]
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)


# Request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all requests"""
    start_time = time.time()
    
    # Rate limiting
    client_ip = request.client.host
    if not rate_limiter.is_allowed(client_ip, settings.RATE_LIMIT_PER_MINUTE, 60):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    
    logger.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s - "
        f"IP: {client_ip}"
    )
    
    # Add security headers
    security_headers = SecurityHeaders.get_security_headers()
    for header, value in security_headers.items():
        response.headers[header] = value
    
    return response


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check database connections
        from .core.database import check_database_health
        db_health = await check_database_health()
        
        # Check market data service
        market_health = await market_data_service.health_check()
        
        # Overall health status
        all_healthy = all(db_health.values()) and all(market_health.values())
        
        return {
            "status": "healthy" if all_healthy else "degraded",
            "timestamp": time.time(),
            "version": settings.APP_VERSION,
            "databases": db_health,
            "market_data": market_health,
            "trading_engine": trading_engine.is_running
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
        )


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to TurtleFlow Pro API",
        "version": settings.APP_VERSION,
        "docs": "/docs" if settings.DEBUG else "Contact admin for API documentation",
        "status": "operational"
    }


# API version info
@app.get("/api/v1/info")
async def api_info():
    """API information endpoint"""
    return {
        "name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "description": "Professional Turtle Trading Bot API",
        "features": [
            "Dual Turtle Trading Systems (20-day & 55-day)",
            "Real-time market data from multiple exchanges",
            "Advanced risk management",
            "Automated position sizing",
            "Pyramid trading",
            "Portfolio analytics"
        ],
        "supported_exchanges": ["Binance", "Coinbase Pro", "Alpha Vantage"],
        "supported_assets": ["Crypto", "Stocks", "Forex"],
        "documentation": "/docs" if settings.DEBUG else None
    }


# Include API routers
app.include_router(
    auth.router,
    prefix=f"{settings.API_V1_STR}/auth",
    tags=["Authentication"]
)

app.include_router(
    trading.router,
    prefix=f"{settings.API_V1_STR}/trading",
    tags=["Trading"]
)

app.include_router(
    portfolio.router,
    prefix=f"{settings.API_V1_STR}/portfolio",
    tags=["Portfolio"]
)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "request_id": getattr(request.state, "request_id", None)
        }
    )


# HTTP exception handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code,
            "path": request.url.path
        }
    )


# WebSocket endpoint for real-time updates
@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket, user_id: int):
    """WebSocket endpoint for real-time trading updates"""
    await websocket.accept()
    
    try:
        # Subscribe to user-specific updates
        async def send_update(data):
            await websocket.send_json(data)
        
        # Keep connection alive and send updates
        while True:
            # In production, this would send real-time portfolio updates,
            # trade notifications, and market data
            await websocket.send_json({
                "type": "heartbeat",
                "timestamp": time.time(),
                "user_id": user_id
            })
            
            await asyncio.sleep(30)  # Send heartbeat every 30 seconds
            
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {e}")
    finally:
        await websocket.close()


# Metrics endpoint for monitoring
@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    try:
        from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
        
        # In production, this would return Prometheus metrics
        # For now, return basic metrics
        return {
            "active_users": 0,  # Would be calculated from database
            "active_positions": 0,  # Would be calculated from database
            "total_trades_today": 0,  # Would be calculated from database
            "system_uptime": time.time(),
            "api_requests_total": 0  # Would be tracked by middleware
        }
    except Exception as e:
        logger.error(f"Metrics error: {e}")
        return {"error": "Metrics unavailable"}


if __name__ == "__main__":
    # Run the application
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True
    )
