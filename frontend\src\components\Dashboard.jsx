import React, { useState, useEffect } from 'react';
import { useQuery } from 'react-query';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Activity,
  Target,
  AlertTriangle,
  BarChart3,
  <PERSON><PERSON>hart
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart as RechartsPieChart, Cell } from 'recharts';
import numeral from 'numeral';

import { portfolioService, tradingService } from '../services/api';
import LoadingSpinner from './LoadingSpinner';
import StatCard from './StatCard';
import TradingSignals from './TradingSignals';

const Dashboard = () => {
  const [timeframe, setTimeframe] = useState('1D');

  // Fetch portfolio data
  const { data: portfolio, isLoading: portfolioLoading } = useQuery(
    'portfolio',
    portfolioService.getPortfolio,
    { refetchInterval: 30000 } // Refresh every 30 seconds
  );

  // Fetch recent trades
  const { data: recentTrades, isLoading: tradesLoading } = useQuery(
    'recent-trades',
    () => tradingService.getRecentTrades(10),
    { refetchInterval: 10000 } // Refresh every 10 seconds
  );

  // Fetch trading signals
  const { data: signals, isLoading: signalsLoading } = useQuery(
    'trading-signals',
    tradingService.getTradingSignals,
    { refetchInterval: 60000 } // Refresh every minute
  );

  // Mock performance data for chart
  const performanceData = [
    { date: '2024-01-01', value: 100000 },
    { date: '2024-01-02', value: 102000 },
    { date: '2024-01-03', value: 98000 },
    { date: '2024-01-04', value: 105000 },
    { date: '2024-01-05', value: 107000 },
    { date: '2024-01-06', value: 103000 },
    { date: '2024-01-07', value: 110000 },
  ];

  // Asset allocation data
  const allocationData = [
    { name: 'BTC', value: 35, color: '#f7931a' },
    { name: 'ETH', value: 25, color: '#627eea' },
    { name: 'ADA', value: 15, color: '#0033ad' },
    { name: 'DOT', value: 10, color: '#e6007a' },
    { name: 'Cash', value: 15, color: '#10b981' },
  ];

  if (portfolioLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Trading Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Monitor your Turtle Trading performance
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
            className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm"
          >
            <option value="1D">1 Day</option>
            <option value="1W">1 Week</option>
            <option value="1M">1 Month</option>
            <option value="3M">3 Months</option>
            <option value="1Y">1 Year</option>
          </select>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Equity"
          value={numeral(portfolio?.total_equity || 0).format('$0,0.00')}
          change={portfolio?.daily_pnl || 0}
          icon={DollarSign}
          color="blue"
        />
        
        <StatCard
          title="Daily P&L"
          value={numeral(portfolio?.daily_pnl || 0).format('$0,0.00')}
          change={portfolio?.daily_pnl || 0}
          icon={portfolio?.daily_pnl >= 0 ? TrendingUp : TrendingDown}
          color={portfolio?.daily_pnl >= 0 ? "green" : "red"}
        />
        
        <StatCard
          title="Win Rate"
          value={`${numeral(portfolio?.win_rate || 0).format('0.0%')}`}
          change={0}
          icon={Target}
          color="purple"
        />
        
        <StatCard
          title="Active Positions"
          value={portfolio?.active_positions || 0}
          change={0}
          icon={Activity}
          color="orange"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Performance Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Portfolio Performance
            </h3>
            <BarChart3 className="h-5 w-5 text-gray-400" />
          </div>
          
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={performanceData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis 
                  dataKey="date" 
                  stroke="#9ca3af"
                  fontSize={12}
                />
                <YAxis 
                  stroke="#9ca3af"
                  fontSize={12}
                  tickFormatter={(value) => numeral(value).format('$0a')}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1f2937',
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#f9fafb'
                  }}
                  formatter={(value) => [numeral(value).format('$0,0'), 'Portfolio Value']}
                />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Asset Allocation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Asset Allocation
            </h3>
            <PieChart className="h-5 w-5 text-gray-400" />
          </div>
          
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <Pie
                  data={allocationData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {allocationData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1f2937',
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#f9fafb'
                  }}
                  formatter={(value) => [`${value}%`, 'Allocation']}
                />
              </RechartsPieChart>
            </ResponsiveContainer>
          </div>
          
          <div className="space-y-2 mt-4">
            {allocationData.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {item.name}
                  </span>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {item.value}%
                </span>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Trading Signals and Recent Trades */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Trading Signals */}
        <TradingSignals signals={signals} isLoading={signalsLoading} />

        {/* Recent Trades */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Recent Trades
            </h3>
            <Activity className="h-5 w-5 text-gray-400" />
          </div>
          
          {tradesLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : (
            <div className="space-y-4">
              {recentTrades?.slice(0, 5).map((trade, index) => (
                <div
                  key={trade.id || index}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${
                      trade.side === 'buy' ? 'bg-green-500' : 'bg-red-500'
                    }`} />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {trade.symbol}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {trade.side?.toUpperCase()} • System {trade.turtle_system}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="font-medium text-gray-900 dark:text-white">
                      {numeral(trade.quantity).format('0,0.00')}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      @ {numeral(trade.price).format('$0,0.00')}
                    </p>
                  </div>
                </div>
              ))}
              
              {(!recentTrades || recentTrades.length === 0) && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  No recent trades
                </div>
              )}
            </div>
          )}
        </motion.div>
      </div>

      {/* Risk Alerts */}
      {portfolio?.current_drawdown > 0.1 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-4"
        >
          <div className="flex items-center space-x-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
            <div>
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200">
                Drawdown Alert
              </h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                Current drawdown: {numeral(portfolio.current_drawdown).format('0.0%')}
              </p>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default Dashboard;
