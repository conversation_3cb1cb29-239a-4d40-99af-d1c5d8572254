"""
TurtleFlow Pro - User Models
SQLAlchemy models for user management and authentication
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

from ..core.database import Base


class User(Base):
    """User model for authentication and profile management"""
    
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Authentication
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(50), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # Profile information
    first_name = Column(String(50))
    last_name = Column(String(50))
    phone_number = Column(String(20))
    
    # Account status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_superuser = Column(Boolean, default=False)
    
    # Trading permissions
    trading_enabled = Column(Boolean, default=False)
    paper_trading_only = Column(Boolean, default=True)
    max_daily_trades = Column(Integer, default=10)
    
    # Account limits
    max_account_value = Column(Float, default=100000.0)
    max_position_size = Column(Float, default=0.25)  # 25% of account
    risk_tolerance = Column(String(20), default="medium")  # low, medium, high
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))
    email_verified_at = Column(DateTime(timezone=True))
    
    # Additional metadata
    timezone = Column(String(50), default="UTC")
    preferred_currency = Column(String(10), default="USD")
    
    # Relationships
    portfolio = relationship("Portfolio", back_populates="user", uselist=False)
    positions = relationship("Position", back_populates="user")
    trades = relationship("Trade", back_populates="user")
    risk_events = relationship("RiskEvent", back_populates="user")
    exchange_accounts = relationship("ExchangeAccount", back_populates="user")
    notifications = relationship("Notification", back_populates="user")


class ExchangeAccount(Base):
    """Exchange account model for storing encrypted API credentials"""
    
    __tablename__ = "exchange_accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, Column("users.id"), nullable=False)
    
    # Exchange details
    exchange_name = Column(String(50), nullable=False)  # binance, coinbase, etc.
    account_name = Column(String(100))  # User-defined name
    
    # Encrypted credentials
    encrypted_api_key = Column(Text, nullable=False)
    encrypted_secret_key = Column(Text, nullable=False)
    encrypted_passphrase = Column(Text)  # For exchanges like Coinbase Pro
    
    # Account settings
    is_active = Column(Boolean, default=True)
    is_testnet = Column(Boolean, default=True)
    is_paper_trading = Column(Boolean, default=True)
    
    # Trading permissions
    spot_trading_enabled = Column(Boolean, default=True)
    futures_trading_enabled = Column(Boolean, default=False)
    margin_trading_enabled = Column(Boolean, default=False)
    
    # Account limits
    daily_trade_limit = Column(Integer, default=100)
    max_order_size = Column(Float)
    
    # Status tracking
    last_connection_test = Column(DateTime(timezone=True))
    connection_status = Column(String(20), default="unknown")  # connected, disconnected, error
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Additional metadata
    notes = Column(Text)
    
    # Relationships
    user = relationship("User", back_populates="exchange_accounts")


class UserSession(Base):
    """User session model for tracking active sessions"""
    
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, Column("users.id"), nullable=False)
    
    # Session details
    session_token = Column(String(255), unique=True, index=True, nullable=False)
    refresh_token = Column(String(255), unique=True, index=True)
    
    # Session metadata
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)
    device_type = Column(String(50))  # web, mobile, api
    
    # Session status
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_activity = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)


class UserPreferences(Base):
    """User preferences model for storing user settings"""
    
    __tablename__ = "user_preferences"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, Column("users.id"), nullable=False, unique=True)
    
    # Trading preferences
    default_turtle_system = Column(Integer, default=1)  # 1 or 2
    auto_trading_enabled = Column(Boolean, default=False)
    risk_per_trade = Column(Float, default=0.01)  # 1%
    max_positions = Column(Integer, default=10)
    
    # Notification preferences
    email_notifications = Column(Boolean, default=True)
    sms_notifications = Column(Boolean, default=False)
    push_notifications = Column(Boolean, default=True)
    
    # Alert preferences
    trade_alerts = Column(Boolean, default=True)
    risk_alerts = Column(Boolean, default=True)
    system_alerts = Column(Boolean, default=True)
    price_alerts = Column(Boolean, default=False)
    
    # Dashboard preferences
    dashboard_layout = Column(JSON)  # Store dashboard configuration
    default_timeframe = Column(String(10), default="1h")
    favorite_symbols = Column(JSON)  # Array of favorite trading pairs
    
    # Display preferences
    theme = Column(String(20), default="dark")  # light, dark
    language = Column(String(10), default="en")
    date_format = Column(String(20), default="YYYY-MM-DD")
    number_format = Column(String(20), default="en-US")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class Notification(Base):
    """Notification model for user alerts and messages"""
    
    __tablename__ = "notifications"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, Column("users.id"), nullable=False)
    
    # Notification details
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    notification_type = Column(String(50), nullable=False)  # trade, risk, system, info
    
    # Priority and status
    priority = Column(String(20), default="medium")  # low, medium, high, urgent
    is_read = Column(Boolean, default=False)
    is_sent = Column(Boolean, default=False)
    
    # Delivery channels
    send_email = Column(Boolean, default=False)
    send_sms = Column(Boolean, default=False)
    send_push = Column(Boolean, default=True)
    
    # Related data
    related_trade_id = Column(Integer)
    related_position_id = Column(Integer)
    related_symbol = Column(String(20))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    read_at = Column(DateTime(timezone=True))
    sent_at = Column(DateTime(timezone=True))
    
    # Additional metadata
    metadata = Column(JSON)  # Store additional notification data
    
    # Relationships
    user = relationship("User", back_populates="notifications")


class AuditLog(Base):
    """Audit log model for tracking user actions"""
    
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer)  # Nullable for system events
    
    # Event details
    event_type = Column(String(50), nullable=False)  # login, trade, config_change, etc.
    event_category = Column(String(30), nullable=False)  # auth, trading, system, admin
    
    # Event data
    description = Column(Text, nullable=False)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    
    # Request details
    endpoint = Column(String(200))
    http_method = Column(String(10))
    request_data = Column(JSON)
    response_status = Column(Integer)
    
    # Additional metadata
    metadata = Column(JSON)
    
    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class APIKey(Base):
    """API key model for external API access"""
    
    __tablename__ = "api_keys"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, Column("users.id"), nullable=False)
    
    # API key details
    key_name = Column(String(100), nullable=False)
    api_key = Column(String(255), unique=True, index=True, nullable=False)
    key_hash = Column(String(255), nullable=False)  # Hashed version for verification
    
    # Permissions
    permissions = Column(JSON)  # Array of allowed permissions
    rate_limit = Column(Integer, default=1000)  # Requests per hour
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Usage tracking
    last_used = Column(DateTime(timezone=True))
    usage_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True))
    
    # Additional metadata
    notes = Column(Text)
