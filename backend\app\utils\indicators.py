"""
TurtleFlow Pro - Technical Indicators
Custom implementation of technical indicators for Turtle Trading
"""

import pandas as pd
import numpy as np
from typing import List, Tuple, Optional, Dict
import logging

logger = logging.getLogger(__name__)


class TechnicalIndicators:
    """Technical indicators for Turtle Trading system"""
    
    @staticmethod
    def true_range(high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
        """
        Calculate True Range (TR)
        TR = max(H-L, H-C_prev, C_prev-L)
        """
        try:
            prev_close = close.shift(1)
            
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(prev_close - low)
            
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            return true_range
        except Exception as e:
            logger.error(f"Error calculating True Range: {e}")
            raise
    
    @staticmethod
    def average_true_range(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
        """
        Calculate Average True Range (ATR)
        ATR = EMA of True Range over specified period
        """
        try:
            tr = TechnicalIndicators.true_range(high, low, close)
            atr = tr.ewm(span=period, adjust=False).mean()
            return atr
        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            raise
    
    @staticmethod
    def donchian_channels(high: pd.Series, low: pd.Series, period: int) -> Tuple[pd.Series, pd.Series]:
        """
        Calculate Donchian Channels (highest high and lowest low over period)
        Returns: (upper_channel, lower_channel)
        """
        try:
            upper_channel = high.rolling(window=period).max()
            lower_channel = low.rolling(window=period).min()
            return upper_channel, lower_channel
        except Exception as e:
            logger.error(f"Error calculating Donchian Channels: {e}")
            raise
    
    @staticmethod
    def turtle_entry_signals(data: pd.DataFrame, system: int = 1) -> pd.DataFrame:
        """
        Generate Turtle Trading entry signals
        System 1: 20-day breakout
        System 2: 55-day breakout
        """
        try:
            if system == 1:
                entry_period = 20
                exit_period = 10
            else:
                entry_period = 55
                exit_period = 20
            
            # Calculate Donchian channels for entry
            entry_high, entry_low = TechnicalIndicators.donchian_channels(
                data['high'], data['low'], entry_period
            )
            
            # Calculate Donchian channels for exit
            exit_high, exit_low = TechnicalIndicators.donchian_channels(
                data['high'], data['low'], exit_period
            )
            
            # Calculate ATR for position sizing
            atr = TechnicalIndicators.average_true_range(
                data['high'], data['low'], data['close']
            )
            
            # Generate signals
            signals = pd.DataFrame(index=data.index)
            signals['entry_long'] = data['close'] > entry_high.shift(1)
            signals['entry_short'] = data['close'] < entry_low.shift(1)
            signals['exit_long'] = data['close'] < exit_low.shift(1)
            signals['exit_short'] = data['close'] > exit_high.shift(1)
            
            # Add price levels
            signals['entry_high_level'] = entry_high
            signals['entry_low_level'] = entry_low
            signals['exit_high_level'] = exit_high
            signals['exit_low_level'] = exit_low
            signals['atr'] = atr
            
            return signals
        except Exception as e:
            logger.error(f"Error generating Turtle entry signals: {e}")
            raise
    
    @staticmethod
    def calculate_position_size(account_equity: float, entry_price: float, 
                              atr: float, risk_per_trade: float = 0.01) -> Dict:
        """
        Calculate Turtle Trading position size
        Position Size = (Account Equity * Risk%) / (Entry Price * ATR * 2)
        """
        try:
            if atr <= 0 or entry_price <= 0:
                raise ValueError("ATR and entry price must be positive")
            
            # Dollar risk per trade
            dollar_risk = account_equity * risk_per_trade
            
            # Stop distance (2 * ATR)
            stop_distance = 2 * atr
            
            # Position size in units
            position_size = dollar_risk / (entry_price * stop_distance / entry_price)
            
            # Stop loss price
            stop_loss_long = entry_price - stop_distance
            stop_loss_short = entry_price + stop_distance
            
            return {
                'position_size': position_size,
                'dollar_risk': dollar_risk,
                'stop_distance': stop_distance,
                'stop_loss_long': stop_loss_long,
                'stop_loss_short': stop_loss_short,
                'risk_per_unit': stop_distance
            }
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            raise
    
    @staticmethod
    def pyramid_signals(current_price: float, entry_price: float, atr: float, 
                       position_side: str, units_held: int, max_units: int = 4) -> Dict:
        """
        Generate pyramid (unit addition) signals
        Add units on favorable 0.5 ATR moves
        """
        try:
            if units_held >= max_units:
                return {'can_add': False, 'reason': 'Maximum units reached'}
            
            pyramid_distance = 0.5 * atr
            
            if position_side.lower() == 'long':
                next_pyramid_price = entry_price + (units_held * pyramid_distance)
                can_add = current_price >= next_pyramid_price
            else:  # short
                next_pyramid_price = entry_price - (units_held * pyramid_distance)
                can_add = current_price <= next_pyramid_price
            
            return {
                'can_add': can_add,
                'next_pyramid_price': next_pyramid_price,
                'pyramid_distance': pyramid_distance,
                'units_held': units_held,
                'max_units': max_units
            }
        except Exception as e:
            logger.error(f"Error calculating pyramid signals: {e}")
            raise
    
    @staticmethod
    def correlation_matrix(price_data: Dict[str, pd.Series], period: int = 20) -> pd.DataFrame:
        """
        Calculate correlation matrix for multiple assets
        Used for risk management and diversification
        """
        try:
            # Calculate returns for each asset
            returns_data = {}
            for symbol, prices in price_data.items():
                returns_data[symbol] = prices.pct_change().dropna()
            
            # Create DataFrame of returns
            returns_df = pd.DataFrame(returns_data)
            
            # Calculate rolling correlation
            correlation_matrix = returns_df.rolling(window=period).corr()
            
            return correlation_matrix
        except Exception as e:
            logger.error(f"Error calculating correlation matrix: {e}")
            raise
    
    @staticmethod
    def volatility_adjusted_position_size(base_position_size: float, current_atr: float, 
                                        average_atr: float, max_adjustment: float = 0.5) -> float:
        """
        Adjust position size based on current volatility vs average volatility
        Higher volatility = smaller position size
        """
        try:
            if average_atr <= 0:
                return base_position_size
            
            volatility_ratio = current_atr / average_atr
            
            # Limit adjustment to prevent extreme position sizes
            adjustment_factor = min(max_adjustment, max(-max_adjustment, 1 - volatility_ratio))
            
            adjusted_size = base_position_size * (1 + adjustment_factor)
            
            return max(0, adjusted_size)  # Ensure non-negative
        except Exception as e:
            logger.error(f"Error adjusting position size for volatility: {e}")
            raise
    
    @staticmethod
    def trailing_stop_price(entry_price: float, current_price: float, atr: float, 
                           position_side: str, trail_multiplier: float = 2.0) -> float:
        """
        Calculate trailing stop price based on ATR
        """
        try:
            trail_distance = atr * trail_multiplier
            
            if position_side.lower() == 'long':
                trailing_stop = current_price - trail_distance
                # Trailing stop can only move up for long positions
                return max(trailing_stop, entry_price - trail_distance)
            else:  # short
                trailing_stop = current_price + trail_distance
                # Trailing stop can only move down for short positions
                return min(trailing_stop, entry_price + trail_distance)
        except Exception as e:
            logger.error(f"Error calculating trailing stop: {e}")
            raise
    
    @staticmethod
    def market_regime_filter(prices: pd.Series, fast_period: int = 20, slow_period: int = 50) -> pd.Series:
        """
        Simple market regime filter using moving averages
        Returns: 1 for uptrend, -1 for downtrend, 0 for sideways
        """
        try:
            fast_ma = prices.rolling(window=fast_period).mean()
            slow_ma = prices.rolling(window=slow_period).mean()
            
            regime = pd.Series(index=prices.index, dtype=int)
            regime[fast_ma > slow_ma] = 1  # Uptrend
            regime[fast_ma < slow_ma] = -1  # Downtrend
            regime[fast_ma == slow_ma] = 0  # Sideways
            
            return regime
        except Exception as e:
            logger.error(f"Error calculating market regime: {e}")
            raise
    
    @staticmethod
    def risk_metrics(returns: pd.Series) -> Dict:
        """
        Calculate risk metrics for performance analysis
        """
        try:
            if len(returns) == 0:
                return {}
            
            # Basic statistics
            total_return = (1 + returns).prod() - 1
            annualized_return = (1 + returns.mean()) ** 252 - 1
            volatility = returns.std() * np.sqrt(252)
            
            # Sharpe ratio (assuming 0% risk-free rate)
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
            
            # Maximum drawdown
            cumulative_returns = (1 + returns).cumprod()
            rolling_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            # Win rate
            winning_trades = (returns > 0).sum()
            total_trades = len(returns)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            return {
                'total_return': total_return,
                'annualized_return': annualized_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'total_trades': total_trades,
                'winning_trades': winning_trades
            }
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            raise
