"""
TurtleFlow Pro - Security Module
JWT authentication, password hashing, and API key encryption
"""

from datetime import datetime, timedelta
from typing import Optional, Union, Any
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from cryptography.fernet import Fernet
import secrets
import hashlib
import hmac
import base64
import logging

from .config import settings

logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# API key encryption
def generate_encryption_key() -> bytes:
    """Generate encryption key for API keys"""
    return Fernet.generate_key()

# Initialize encryption (in production, store key securely)
ENCRYPTION_KEY = settings.SECRET_KEY.encode()[:32]  # Use first 32 chars
fernet = Fernet(base64.urlsafe_b64encode(ENCRYPTION_KEY))


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error(f"Password verification error: {e}")
        return False


def get_password_hash(password: str) -> str:
    """Hash a password"""
    try:
        return pwd_context.hash(password)
    except Exception as e:
        logger.error(f"Password hashing error: {e}")
        raise


def create_access_token(
    subject: Union[str, Any], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """Create JWT access token"""
    try:
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )
        
        to_encode = {
            "exp": expire,
            "sub": str(subject),
            "type": "access"
        }
        
        encoded_jwt = jwt.encode(
            to_encode, 
            settings.SECRET_KEY, 
            algorithm=settings.ALGORITHM
        )
        return encoded_jwt
    except Exception as e:
        logger.error(f"Access token creation error: {e}")
        raise


def create_refresh_token(subject: Union[str, Any]) -> str:
    """Create JWT refresh token"""
    try:
        expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        
        to_encode = {
            "exp": expire,
            "sub": str(subject),
            "type": "refresh"
        }
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        return encoded_jwt
    except Exception as e:
        logger.error(f"Refresh token creation error: {e}")
        raise


def verify_token(token: str) -> Optional[str]:
    """Verify JWT token and return subject"""
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        subject: str = payload.get("sub")
        token_type: str = payload.get("type")
        
        if subject is None:
            return None
            
        return subject
    except JWTError as e:
        logger.error(f"Token verification error: {e}")
        return None


def encrypt_api_key(api_key: str) -> str:
    """Encrypt API key for secure storage"""
    try:
        encrypted_key = fernet.encrypt(api_key.encode())
        return base64.urlsafe_b64encode(encrypted_key).decode()
    except Exception as e:
        logger.error(f"API key encryption error: {e}")
        raise


def decrypt_api_key(encrypted_key: str) -> str:
    """Decrypt API key for use"""
    try:
        decoded_key = base64.urlsafe_b64decode(encrypted_key.encode())
        decrypted_key = fernet.decrypt(decoded_key)
        return decrypted_key.decode()
    except Exception as e:
        logger.error(f"API key decryption error: {e}")
        raise


def generate_api_key() -> str:
    """Generate secure API key"""
    return secrets.token_urlsafe(32)


def generate_webhook_signature(payload: str, secret: str) -> str:
    """Generate webhook signature for verification"""
    try:
        signature = hmac.new(
            secret.encode(),
            payload.encode(),
            hashlib.sha256
        ).hexdigest()
        return f"sha256={signature}"
    except Exception as e:
        logger.error(f"Webhook signature generation error: {e}")
        raise


def verify_webhook_signature(payload: str, signature: str, secret: str) -> bool:
    """Verify webhook signature"""
    try:
        expected_signature = generate_webhook_signature(payload, secret)
        return hmac.compare_digest(signature, expected_signature)
    except Exception as e:
        logger.error(f"Webhook signature verification error: {e}")
        return False


class SecurityHeaders:
    """Security headers for API responses"""
    
    @staticmethod
    def get_security_headers() -> dict:
        """Get security headers"""
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }


class RateLimiter:
    """Simple rate limiter for API endpoints"""
    
    def __init__(self):
        self.requests = {}
    
    def is_allowed(self, identifier: str, limit: int = 100, window: int = 60) -> bool:
        """Check if request is allowed based on rate limit"""
        now = datetime.utcnow()
        
        if identifier not in self.requests:
            self.requests[identifier] = []
        
        # Clean old requests
        self.requests[identifier] = [
            req_time for req_time in self.requests[identifier]
            if (now - req_time).seconds < window
        ]
        
        # Check limit
        if len(self.requests[identifier]) >= limit:
            return False
        
        # Add current request
        self.requests[identifier].append(now)
        return True


# Global rate limiter instance
rate_limiter = RateLimiter()


def validate_trading_permissions(user_permissions: list, required_permission: str) -> bool:
    """Validate user has required trading permissions"""
    try:
        return required_permission in user_permissions or "admin" in user_permissions
    except Exception as e:
        logger.error(f"Permission validation error: {e}")
        return False


def sanitize_input(input_string: str) -> str:
    """Sanitize user input to prevent injection attacks"""
    try:
        # Remove potentially dangerous characters
        dangerous_chars = ["<", ">", "&", "\"", "'", "/", "\\", ";", "(", ")", "{", "}"]
        sanitized = input_string
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, "")
        
        return sanitized.strip()
    except Exception as e:
        logger.error(f"Input sanitization error: {e}")
        return ""


def generate_csrf_token() -> str:
    """Generate CSRF token"""
    return secrets.token_urlsafe(32)


def verify_csrf_token(token: str, expected_token: str) -> bool:
    """Verify CSRF token"""
    try:
        return hmac.compare_digest(token, expected_token)
    except Exception as e:
        logger.error(f"CSRF token verification error: {e}")
        return False


# Security audit logging
def log_security_event(event_type: str, user_id: Optional[str], details: dict):
    """Log security events for audit trail"""
    try:
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type,
            "user_id": user_id,
            "details": details
        }
        
        logger.warning(f"SECURITY_EVENT: {log_entry}")
    except Exception as e:
        logger.error(f"Security event logging error: {e}")


# API key validation for exchanges
def validate_exchange_credentials(exchange: str, credentials: dict) -> bool:
    """Validate exchange API credentials format"""
    try:
        if exchange.lower() == "binance":
            return "api_key" in credentials and "secret_key" in credentials
        elif exchange.lower() == "coinbase":
            return all(key in credentials for key in ["api_key", "secret_key", "passphrase"])
        elif exchange.lower() == "alpha_vantage":
            return "api_key" in credentials
        else:
            return False
    except Exception as e:
        logger.error(f"Exchange credentials validation error: {e}")
        return False
