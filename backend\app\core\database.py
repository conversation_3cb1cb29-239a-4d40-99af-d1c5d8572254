"""
TurtleFlow Pro - Database Configuration
SQLAlchemy setup for PostgreSQL and InfluxDB connections
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import St<PERSON>Pool
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import SYNCHRONOUS
import redis.asyncio as redis
from typing import Generator, Optional
import logging

from .config import settings

logger = logging.getLogger(__name__)

# PostgreSQL Database Setup
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=10,
    max_overflow=20,
    echo=settings.DEBUG
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Metadata for migrations
metadata = MetaData()


def get_db() -> Generator[Session, None, None]:
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


# InfluxDB Setup for Time Series Data
class InfluxDBManager:
    """InfluxDB client manager for market data storage"""
    
    def __init__(self):
        self.client: Optional[InfluxDBClient] = None
        self.write_api = None
        self.query_api = None
        
    def connect(self):
        """Connect to InfluxDB"""
        try:
            self.client = InfluxDBClient(
                url=settings.INFLUXDB_URL,
                token=settings.INFLUXDB_TOKEN,
                org=settings.INFLUXDB_ORG
            )
            self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
            self.query_api = self.client.query_api()
            logger.info("Connected to InfluxDB successfully")
        except Exception as e:
            logger.error(f"Failed to connect to InfluxDB: {e}")
            raise
    
    def disconnect(self):
        """Disconnect from InfluxDB"""
        if self.client:
            self.client.close()
            logger.info("Disconnected from InfluxDB")
    
    def write_market_data(self, symbol: str, timestamp, open_price: float, 
                         high: float, low: float, close: float, volume: float):
        """Write OHLCV data to InfluxDB"""
        try:
            point = Point("market_data") \
                .tag("symbol", symbol) \
                .field("open", open_price) \
                .field("high", high) \
                .field("low", low) \
                .field("close", close) \
                .field("volume", volume) \
                .time(timestamp)
            
            self.write_api.write(bucket=settings.INFLUXDB_BUCKET, record=point)
        except Exception as e:
            logger.error(f"Failed to write market data for {symbol}: {e}")
            raise
    
    def query_market_data(self, symbol: str, start_time: str, end_time: str):
        """Query market data from InfluxDB"""
        try:
            query = f'''
                from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: {start_time}, stop: {end_time})
                |> filter(fn: (r) => r._measurement == "market_data")
                |> filter(fn: (r) => r.symbol == "{symbol}")
                |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
            '''
            
            result = self.query_api.query(query)
            return result
        except Exception as e:
            logger.error(f"Failed to query market data for {symbol}: {e}")
            raise


# Redis Setup for Caching
class RedisManager:
    """Redis client manager for caching and session storage"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
    
    async def connect(self):
        """Connect to Redis"""
        try:
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("Connected to Redis successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from Redis"""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Disconnected from Redis")
    
    async def set_cache(self, key: str, value: str, expire: int = 3600):
        """Set cache value with expiration"""
        try:
            await self.redis_client.setex(key, expire, value)
        except Exception as e:
            logger.error(f"Failed to set cache for key {key}: {e}")
            raise
    
    async def get_cache(self, key: str) -> Optional[str]:
        """Get cache value"""
        try:
            return await self.redis_client.get(key)
        except Exception as e:
            logger.error(f"Failed to get cache for key {key}: {e}")
            return None
    
    async def delete_cache(self, key: str):
        """Delete cache key"""
        try:
            await self.redis_client.delete(key)
        except Exception as e:
            logger.error(f"Failed to delete cache for key {key}: {e}")
    
    async def set_market_data(self, symbol: str, data: dict, expire: int = 60):
        """Cache market data"""
        key = f"market_data:{symbol}"
        await self.set_cache(key, str(data), expire)
    
    async def get_market_data(self, symbol: str) -> Optional[dict]:
        """Get cached market data"""
        key = f"market_data:{symbol}"
        data = await self.get_cache(key)
        return eval(data) if data else None


# Global instances
influx_manager = InfluxDBManager()
redis_manager = RedisManager()


async def init_databases():
    """Initialize all database connections"""
    try:
        # Connect to InfluxDB
        influx_manager.connect()
        
        # Connect to Redis
        await redis_manager.connect()
        
        logger.info("All databases initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize databases: {e}")
        raise


async def close_databases():
    """Close all database connections"""
    try:
        # Disconnect from InfluxDB
        influx_manager.disconnect()
        
        # Disconnect from Redis
        await redis_manager.disconnect()
        
        logger.info("All databases closed successfully")
    except Exception as e:
        logger.error(f"Error closing databases: {e}")


def create_tables():
    """Create all database tables"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


# Database health check
async def check_database_health() -> dict:
    """Check health of all database connections"""
    health_status = {
        "postgresql": False,
        "influxdb": False,
        "redis": False
    }
    
    # Check PostgreSQL
    try:
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        health_status["postgresql"] = True
    except Exception as e:
        logger.error(f"PostgreSQL health check failed: {e}")
    
    # Check InfluxDB
    try:
        if influx_manager.client:
            influx_manager.client.ping()
            health_status["influxdb"] = True
    except Exception as e:
        logger.error(f"InfluxDB health check failed: {e}")
    
    # Check Redis
    try:
        if redis_manager.redis_client:
            await redis_manager.redis_client.ping()
            health_status["redis"] = True
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
    
    return health_status
