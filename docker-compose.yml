version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: turtleflow_postgres
    environment:
      POSTGRES_DB: turtleflow_db
      POSTGRES_USER: turtleflow
      POSTGRES_PASSWORD: password123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - turtleflow_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U turtleflow -d turtleflow_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: turtleflow_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - turtleflow_network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis123
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # InfluxDB Time Series Database
  influxdb:
    image: influxdb:2.7-alpine
    container_name: turtleflow_influxdb
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: password123
      DOCKER_INFLUXDB_INIT_ORG: turtleflow
      DOCKER_INFLUXDB_INIT_BUCKET: market_data
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: turtleflow-token-123
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    networks:
      - turtleflow_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: turtleflow_backend
    environment:
      - DEBUG=false
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=turtleflow
      - POSTGRES_PASSWORD=password123
      - POSTGRES_DB=turtleflow_db
      - REDIS_HOST=redis
      - REDIS_PASSWORD=redis123
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=turtleflow-token-123
      - INFLUXDB_ORG=turtleflow
      - INFLUXDB_BUCKET=market_data
      - SECRET_KEY=your-super-secret-key-change-in-production
      - BINANCE_TESTNET=true
      - COINBASE_SANDBOX=true
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    networks:
      - turtleflow_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      influxdb:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: turtleflow_frontend
    environment:
      - VITE_API_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:8000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - turtleflow_network
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: turtleflow_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - turtleflow_network
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: turtleflow_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - turtleflow_network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: turtleflow_grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - turtleflow_network
    depends_on:
      - prometheus
    restart: unless-stopped

  # Celery Worker for Background Tasks
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: turtleflow_celery_worker
    environment:
      - DEBUG=false
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=turtleflow
      - POSTGRES_PASSWORD=password123
      - POSTGRES_DB=turtleflow_db
      - REDIS_HOST=redis
      - REDIS_PASSWORD=redis123
    volumes:
      - ./backend:/app
    networks:
      - turtleflow_network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    command: celery -A app.main worker --loglevel=info

  # Celery Beat Scheduler
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: turtleflow_celery_beat
    environment:
      - DEBUG=false
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=turtleflow
      - POSTGRES_PASSWORD=password123
      - POSTGRES_DB=turtleflow_db
      - REDIS_HOST=redis
      - REDIS_PASSWORD=redis123
    volumes:
      - ./backend:/app
    networks:
      - turtleflow_network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    command: celery -A app.main beat --loglevel=info

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  influxdb_data:
    driver: local
  influxdb_config:
    driver: local
  backend_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  turtleflow_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
