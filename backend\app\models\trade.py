"""
TurtleFlow Pro - Trade Models
SQLAlchemy models for trading data
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import enum

from ..core.database import Base


class TradeStatus(enum.Enum):
    """Trade status enumeration"""
    PENDING = "pending"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class TradeSide(enum.Enum):
    """Trade side enumeration"""
    BUY = "buy"
    SELL = "sell"


class TradeType(enum.Enum):
    """Trade type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"
    OCO = "oco"  # One-Cancels-Other


class TurtleSystem(enum.Enum):
    """Turtle trading system enumeration"""
    SYSTEM_1 = 1  # 20-day breakout
    SYSTEM_2 = 2  # 55-day breakout


class Trade(Base):
    """Trade model for storing individual trades"""
    
    __tablename__ = "trades"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Trade identification
    exchange_order_id = Column(String(100), unique=True, index=True)
    client_order_id = Column(String(100), unique=True, index=True)
    
    # Trading pair and exchange
    symbol = Column(String(20), nullable=False, index=True)
    exchange = Column(String(20), nullable=False)
    
    # Trade details
    side = Column(Enum(TradeSide), nullable=False)
    trade_type = Column(Enum(TradeType), nullable=False)
    status = Column(Enum(TradeStatus), default=TradeStatus.PENDING)
    
    # Quantities and prices
    quantity = Column(Float, nullable=False)
    filled_quantity = Column(Float, default=0.0)
    price = Column(Float)  # Limit price (null for market orders)
    average_fill_price = Column(Float)
    
    # Stop loss and take profit
    stop_loss_price = Column(Float)
    take_profit_price = Column(Float)
    
    # Turtle trading specific
    turtle_system = Column(Enum(TurtleSystem))
    unit_number = Column(Integer)  # For pyramiding
    entry_signal_price = Column(Float)
    atr_value = Column(Float)
    
    # Financial details
    commission = Column(Float, default=0.0)
    realized_pnl = Column(Float, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    filled_at = Column(DateTime(timezone=True))
    
    # Additional metadata
    notes = Column(Text)
    
    # Relationships
    user = relationship("User", back_populates="trades")
    position = relationship("Position", back_populates="trades")


class Position(Base):
    """Position model for tracking open positions"""
    
    __tablename__ = "positions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Position identification
    symbol = Column(String(20), nullable=False, index=True)
    exchange = Column(String(20), nullable=False)
    
    # Position details
    side = Column(Enum(TradeSide), nullable=False)
    quantity = Column(Float, nullable=False)
    average_entry_price = Column(Float, nullable=False)
    current_price = Column(Float)
    
    # Risk management
    stop_loss_price = Column(Float)
    take_profit_price = Column(Float)
    trailing_stop_price = Column(Float)
    
    # Turtle trading specific
    turtle_system = Column(Enum(TurtleSystem), nullable=False)
    total_units = Column(Integer, default=1)
    max_units = Column(Integer, default=4)
    last_pyramid_price = Column(Float)
    
    # P&L tracking
    unrealized_pnl = Column(Float, default=0.0)
    realized_pnl = Column(Float, default=0.0)
    total_commission = Column(Float, default=0.0)
    
    # Position status
    is_open = Column(Boolean, default=True)
    
    # Timestamps
    opened_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    closed_at = Column(DateTime(timezone=True))
    
    # Additional metadata
    notes = Column(Text)
    
    # Relationships
    user = relationship("User", back_populates="positions")
    trades = relationship("Trade", back_populates="position")


class TradingSignal(Base):
    """Trading signal model for storing generated signals"""
    
    __tablename__ = "trading_signals"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Signal identification
    symbol = Column(String(20), nullable=False, index=True)
    timeframe = Column(String(10), nullable=False)  # 1m, 5m, 1h, 1d, etc.
    
    # Signal details
    signal_type = Column(String(20), nullable=False)  # entry_long, entry_short, exit_long, exit_short
    turtle_system = Column(Enum(TurtleSystem), nullable=False)
    
    # Price levels
    signal_price = Column(Float, nullable=False)
    stop_loss_price = Column(Float)
    take_profit_price = Column(Float)
    
    # Technical indicators
    atr_value = Column(Float)
    breakout_level = Column(Float)
    exit_level = Column(Float)
    
    # Signal strength and confidence
    strength = Column(Float, default=1.0)  # 0.0 to 1.0
    confidence = Column(Float, default=1.0)  # 0.0 to 1.0
    
    # Status
    is_active = Column(Boolean, default=True)
    is_executed = Column(Boolean, default=False)
    
    # Timestamps
    generated_at = Column(DateTime(timezone=True), server_default=func.now())
    executed_at = Column(DateTime(timezone=True))
    expires_at = Column(DateTime(timezone=True))
    
    # Additional metadata
    notes = Column(Text)


class Portfolio(Base):
    """Portfolio model for tracking overall portfolio performance"""
    
    __tablename__ = "portfolios"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, unique=True)
    
    # Portfolio values
    total_equity = Column(Float, nullable=False, default=0.0)
    available_cash = Column(Float, nullable=False, default=0.0)
    invested_amount = Column(Float, default=0.0)
    
    # Performance metrics
    total_return = Column(Float, default=0.0)
    total_return_percentage = Column(Float, default=0.0)
    daily_pnl = Column(Float, default=0.0)
    unrealized_pnl = Column(Float, default=0.0)
    realized_pnl = Column(Float, default=0.0)
    
    # Risk metrics
    max_drawdown = Column(Float, default=0.0)
    current_drawdown = Column(Float, default=0.0)
    sharpe_ratio = Column(Float, default=0.0)
    
    # Trading statistics
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    win_rate = Column(Float, default=0.0)
    
    # Risk management
    daily_loss_limit = Column(Float)
    max_position_size = Column(Float)
    risk_per_trade = Column(Float, default=0.01)
    
    # Status
    is_active = Column(Boolean, default=True)
    auto_trading_enabled = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="portfolio")


class RiskEvent(Base):
    """Risk event model for tracking risk management events"""
    
    __tablename__ = "risk_events"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Event details
    event_type = Column(String(50), nullable=False)  # stop_loss, daily_limit, max_drawdown, etc.
    severity = Column(String(20), nullable=False)  # low, medium, high, critical
    
    # Event data
    symbol = Column(String(20))
    triggered_value = Column(Float)
    threshold_value = Column(Float)
    
    # Actions taken
    action_taken = Column(String(100))
    positions_closed = Column(Integer, default=0)
    trading_halted = Column(Boolean, default=False)
    
    # Status
    is_resolved = Column(Boolean, default=False)
    
    # Timestamps
    triggered_at = Column(DateTime(timezone=True), server_default=func.now())
    resolved_at = Column(DateTime(timezone=True))
    
    # Additional details
    description = Column(Text)
    
    # Relationships
    user = relationship("User", back_populates="risk_events")
