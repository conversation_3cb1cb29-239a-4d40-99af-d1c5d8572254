/**
 * TurtleFlow Pro - API Service
 * Centralized API communication layer
 */

import axios from 'axios';
import toast from 'react-hot-toast';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';
const API_VERSION = '/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: `${API_BASE_URL}${API_VERSION}`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response } = error;
    
    if (response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('token');
      window.location.href = '/login';
      toast.error('Session expired. Please login again.');
    } else if (response?.status === 403) {
      toast.error('Access denied. Insufficient permissions.');
    } else if (response?.status === 429) {
      toast.error('Too many requests. Please try again later.');
    } else if (response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.code === 'NETWORK_ERROR') {
      toast.error('Network error. Please check your connection.');
    }
    
    return Promise.reject(error);
  }
);

// Authentication Service
export const authService = {
  async login(email, password) {
    try {
      const response = await api.post('/auth/login', {
        email,
        password,
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Login failed');
    }
  },

  async register(userData) {
    try {
      const response = await api.post('/auth/register', userData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Registration failed');
    }
  },

  async getCurrentUser() {
    try {
      const response = await api.get('/auth/me');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to get user info');
    }
  },

  async refreshToken() {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      const response = await api.post('/auth/refresh', {
        refresh_token: refreshToken,
      });
      return response.data;
    } catch (error) {
      throw new Error('Token refresh failed');
    }
  },

  async logout() {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
    }
  },
};

// Portfolio Service
export const portfolioService = {
  async getPortfolio() {
    try {
      const response = await api.get('/portfolio');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch portfolio');
    }
  },

  async getPositions() {
    try {
      const response = await api.get('/portfolio/positions');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch positions');
    }
  },

  async getPerformance(timeframe = '1M') {
    try {
      const response = await api.get(`/portfolio/performance?timeframe=${timeframe}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch performance data');
    }
  },

  async updateRiskSettings(settings) {
    try {
      const response = await api.put('/portfolio/risk-settings', settings);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to update risk settings');
    }
  },
};

// Trading Service
export const tradingService = {
  async getTradingSignals() {
    try {
      const response = await api.get('/trading/signals');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch trading signals');
    }
  },

  async getRecentTrades(limit = 10) {
    try {
      const response = await api.get(`/trading/trades?limit=${limit}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch recent trades');
    }
  },

  async placeTrade(tradeData) {
    try {
      const response = await api.post('/trading/place-order', tradeData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to place trade');
    }
  },

  async cancelTrade(tradeId) {
    try {
      const response = await api.delete(`/trading/trades/${tradeId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to cancel trade');
    }
  },

  async getMarketData(symbol, timeframe = '1h') {
    try {
      const response = await api.get(`/trading/market-data/${symbol}?timeframe=${timeframe}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch market data');
    }
  },

  async enableAutoTrading() {
    try {
      const response = await api.post('/trading/auto-trading/enable');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to enable auto trading');
    }
  },

  async disableAutoTrading() {
    try {
      const response = await api.post('/trading/auto-trading/disable');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to disable auto trading');
    }
  },
};

// Market Data Service
export const marketDataService = {
  async getCurrentPrices(symbols) {
    try {
      const response = await api.post('/market-data/current-prices', { symbols });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch current prices');
    }
  },

  async getHistoricalData(symbol, timeframe = '1h', limit = 100) {
    try {
      const response = await api.get(
        `/market-data/historical/${symbol}?timeframe=${timeframe}&limit=${limit}`
      );
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch historical data');
    }
  },

  async getWatchlist() {
    try {
      const response = await api.get('/market-data/watchlist');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch watchlist');
    }
  },

  async addToWatchlist(symbol) {
    try {
      const response = await api.post('/market-data/watchlist', { symbol });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to add to watchlist');
    }
  },

  async removeFromWatchlist(symbol) {
    try {
      const response = await api.delete(`/market-data/watchlist/${symbol}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to remove from watchlist');
    }
  },
};

// Settings Service
export const settingsService = {
  async getUserSettings() {
    try {
      const response = await api.get('/settings');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch settings');
    }
  },

  async updateUserSettings(settings) {
    try {
      const response = await api.put('/settings', settings);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to update settings');
    }
  },

  async getExchangeAccounts() {
    try {
      const response = await api.get('/settings/exchange-accounts');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch exchange accounts');
    }
  },

  async addExchangeAccount(accountData) {
    try {
      const response = await api.post('/settings/exchange-accounts', accountData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to add exchange account');
    }
  },

  async testExchangeConnection(accountId) {
    try {
      const response = await api.post(`/settings/exchange-accounts/${accountId}/test`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Connection test failed');
    }
  },
};

// WebSocket Service
export class WebSocketService {
  constructor() {
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000;
    this.subscribers = new Map();
  }

  connect(userId) {
    const wsUrl = `${import.meta.env.VITE_WS_URL || 'ws://localhost:8000'}/ws/${userId}`;
    
    try {
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
        toast.success('Real-time connection established');
      };
      
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.notifySubscribers(data);
        } catch (error) {
          console.error('WebSocket message parsing error:', error);
        }
      };
      
      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.reconnect(userId);
      };
      
      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    } catch (error) {
      console.error('WebSocket connection error:', error);
    }
  }

  reconnect(userId) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect(userId);
      }, this.reconnectInterval);
    } else {
      toast.error('Unable to establish real-time connection');
    }
  }

  subscribe(event, callback) {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, []);
    }
    this.subscribers.get(event).push(callback);
  }

  unsubscribe(event, callback) {
    if (this.subscribers.has(event)) {
      const callbacks = this.subscribers.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  notifySubscribers(data) {
    const { type } = data;
    if (this.subscribers.has(type)) {
      this.subscribers.get(type).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Subscriber callback error:', error);
        }
      });
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.subscribers.clear();
  }
}

// Export WebSocket instance
export const wsService = new WebSocketService();

// Health check
export const healthService = {
  async checkHealth() {
    try {
      const response = await axios.get(`${API_BASE_URL}/health`);
      return response.data;
    } catch (error) {
      throw new Error('Health check failed');
    }
  },
};

export default api;
