"""
TurtleFlow Pro - Trading Engine
Core trading logic implementing Turtle Trading system
"""

import asyncio
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import logging
import uuid
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..core.config import settings, SUPPORTED_PAIRS
from ..models.trade import Trade, Position, TradingSignal, TradeStatus, TradeSide, TurtleSystem
from ..models.user import User
from ..services.market_data import market_data_service
from ..services.risk_manager import risk_manager
from ..services.portfolio_manager import portfolio_manager
from ..utils.indicators import TechnicalIndicators

logger = logging.getLogger(__name__)


class TradingEngine:
    """Main trading engine for executing Turtle Trading strategy"""
    
    def __init__(self):
        self.is_running = False
        self.active_positions = {}
        self.pending_orders = {}
        self.last_trade_profitable = {}  # Track last trade profitability for System 1 filter
        
    async def start(self):
        """Start the trading engine"""
        try:
            self.is_running = True
            logger.info("Trading engine started")
            
            # Start main trading loop
            await self._main_trading_loop()
        except Exception as e:
            logger.error(f"Error starting trading engine: {e}")
            raise
    
    async def stop(self):
        """Stop the trading engine"""
        self.is_running = False
        logger.info("Trading engine stopped")
    
    async def _main_trading_loop(self):
        """Main trading loop - runs continuously"""
        while self.is_running:
            try:
                # Get all active users with trading enabled
                db = next(get_db())
                active_users = db.query(User).filter(
                    User.is_active == True,
                    User.trading_enabled == True
                ).all()
                
                # Process each user's trading
                for user in active_users:
                    await self._process_user_trading(user, db)
                
                # Wait before next iteration
                await asyncio.sleep(60)  # Run every minute
                
            except Exception as e:
                logger.error(f"Error in main trading loop: {e}")
                await asyncio.sleep(60)
    
    async def _process_user_trading(self, user: User, db: Session):
        """Process trading for a specific user"""
        try:
            # Check if user has sufficient funds
            portfolio = await portfolio_manager.get_portfolio(user.id, db)
            if portfolio.available_cash < 1000:  # Minimum balance
                return
            
            # Get user's trading pairs
            trading_pairs = self._get_user_trading_pairs(user)
            
            # Process each trading pair
            for pair in trading_pairs:
                await self._process_trading_pair(user, pair, db)
                
        except Exception as e:
            logger.error(f"Error processing trading for user {user.id}: {e}")
    
    def _get_user_trading_pairs(self, user: User) -> List[str]:
        """Get trading pairs for user based on preferences"""
        # For now, return default crypto pairs
        # In production, this would be based on user preferences
        return SUPPORTED_PAIRS["crypto"][:5]  # Top 5 crypto pairs
    
    async def _process_trading_pair(self, user: User, symbol: str, db: Session):
        """Process trading signals for a specific trading pair"""
        try:
            # Generate signals for both systems
            system_1_signals = await market_data_service.calculate_signals(symbol, 1)
            system_2_signals = await market_data_service.calculate_signals(symbol, 2)
            
            # Process System 1 signals
            await self._process_system_signals(user, symbol, system_1_signals, 1, db)
            
            # Process System 2 signals
            await self._process_system_signals(user, symbol, system_2_signals, 2, db)
            
            # Check exit signals for existing positions
            await self._check_exit_signals(user, symbol, system_1_signals, system_2_signals, db)
            
            # Check pyramid opportunities
            await self._check_pyramid_signals(user, symbol, db)
            
        except Exception as e:
            logger.error(f"Error processing trading pair {symbol} for user {user.id}: {e}")
    
    async def _process_system_signals(self, user: User, symbol: str, signals: Dict, 
                                    system: int, db: Session):
        """Process entry signals for a specific system"""
        try:
            current_price = signals['current_price']
            atr = signals['atr']
            
            # Check for long entry signal
            if signals['entry_long']:
                # Apply System 1 filter (skip if last trade was profitable)
                if system == 1:
                    last_trade_key = f"{user.id}:{symbol}:system_1"
                    if self.last_trade_profitable.get(last_trade_key, False):
                        logger.info(f"Skipping System 1 long entry for {symbol} - last trade was profitable")
                        return
                
                await self._execute_entry_signal(
                    user, symbol, TradeSide.BUY, current_price, atr, system, db
                )
            
            # Check for short entry signal
            if signals['entry_short']:
                # Apply System 1 filter
                if system == 1:
                    last_trade_key = f"{user.id}:{symbol}:system_1"
                    if self.last_trade_profitable.get(last_trade_key, False):
                        logger.info(f"Skipping System 1 short entry for {symbol} - last trade was profitable")
                        return
                
                await self._execute_entry_signal(
                    user, symbol, TradeSide.SELL, current_price, atr, system, db
                )
                
        except Exception as e:
            logger.error(f"Error processing system {system} signals: {e}")
    
    async def _execute_entry_signal(self, user: User, symbol: str, side: TradeSide, 
                                  entry_price: float, atr: float, system: int, db: Session):
        """Execute entry signal by placing order"""
        try:
            # Check if position already exists
            existing_position = db.query(Position).filter(
                Position.user_id == user.id,
                Position.symbol == symbol,
                Position.is_open == True,
                Position.turtle_system == TurtleSystem(system)
            ).first()
            
            if existing_position:
                logger.info(f"Position already exists for {symbol} system {system}")
                return
            
            # Get portfolio for position sizing
            portfolio = await portfolio_manager.get_portfolio(user.id, db)
            
            # Calculate position size
            position_calc = TechnicalIndicators.calculate_position_size(
                portfolio.total_equity,
                entry_price,
                atr,
                portfolio.risk_per_trade
            )
            
            # Risk management check
            risk_check = await risk_manager.validate_trade(
                user.id, symbol, side, position_calc['position_size'], entry_price, db
            )
            
            if not risk_check['approved']:
                logger.warning(f"Trade rejected by risk manager: {risk_check['reason']}")
                return
            
            # Create trade order
            trade = Trade(
                user_id=user.id,
                client_order_id=str(uuid.uuid4()),
                symbol=symbol,
                exchange='binance',  # Default exchange
                side=side,
                trade_type='market',
                status=TradeStatus.PENDING,
                quantity=position_calc['position_size'],
                price=entry_price,
                turtle_system=TurtleSystem(system),
                unit_number=1,
                entry_signal_price=entry_price,
                atr_value=atr,
                stop_loss_price=position_calc['stop_loss_long'] if side == TradeSide.BUY else position_calc['stop_loss_short']
            )
            
            db.add(trade)
            db.commit()
            
            # Execute the trade (in production, this would place actual order)
            await self._execute_trade_order(trade, db)
            
            logger.info(f"Entry signal executed: {symbol} {side.value} system {system}")
            
        except Exception as e:
            logger.error(f"Error executing entry signal: {e}")
            db.rollback()
    
    async def _execute_trade_order(self, trade: Trade, db: Session):
        """Execute trade order (mock implementation)"""
        try:
            # In production, this would place actual order on exchange
            # For now, we'll simulate immediate fill
            
            trade.status = TradeStatus.FILLED
            trade.filled_quantity = trade.quantity
            trade.average_fill_price = trade.price
            trade.filled_at = datetime.utcnow()
            
            # Create or update position
            await self._create_or_update_position(trade, db)
            
            # Update portfolio
            await portfolio_manager.update_portfolio_after_trade(trade, db)
            
            db.commit()
            
            logger.info(f"Trade executed: {trade.symbol} {trade.side.value} {trade.quantity}")
            
        except Exception as e:
            logger.error(f"Error executing trade order: {e}")
            db.rollback()
    
    async def _create_or_update_position(self, trade: Trade, db: Session):
        """Create new position or update existing position"""
        try:
            position = db.query(Position).filter(
                Position.user_id == trade.user_id,
                Position.symbol == trade.symbol,
                Position.turtle_system == trade.turtle_system,
                Position.is_open == True
            ).first()
            
            if position:
                # Update existing position (pyramid)
                total_cost = (position.quantity * position.average_entry_price) + (trade.quantity * trade.average_fill_price)
                total_quantity = position.quantity + trade.quantity
                position.average_entry_price = total_cost / total_quantity
                position.quantity = total_quantity
                position.total_units += 1
                position.last_pyramid_price = trade.average_fill_price
            else:
                # Create new position
                position = Position(
                    user_id=trade.user_id,
                    symbol=trade.symbol,
                    exchange=trade.exchange,
                    side=trade.side,
                    quantity=trade.quantity,
                    average_entry_price=trade.average_fill_price,
                    turtle_system=trade.turtle_system,
                    total_units=1,
                    stop_loss_price=trade.stop_loss_price
                )
                db.add(position)
            
            # Link trade to position
            trade.position = position
            
        except Exception as e:
            logger.error(f"Error creating/updating position: {e}")
            raise
    
    async def _check_exit_signals(self, user: User, symbol: str, system_1_signals: Dict, 
                                system_2_signals: Dict, db: Session):
        """Check exit signals for existing positions"""
        try:
            positions = db.query(Position).filter(
                Position.user_id == user.id,
                Position.symbol == symbol,
                Position.is_open == True
            ).all()
            
            for position in positions:
                signals = system_1_signals if position.turtle_system == TurtleSystem.SYSTEM_1 else system_2_signals
                
                should_exit = False
                exit_reason = ""
                
                # Check system exit signals
                if position.side == TradeSide.BUY and signals['exit_long']:
                    should_exit = True
                    exit_reason = "System exit signal"
                elif position.side == TradeSide.SELL and signals['exit_short']:
                    should_exit = True
                    exit_reason = "System exit signal"
                
                # Check stop loss
                current_price = signals['current_price']
                if position.side == TradeSide.BUY and current_price <= position.stop_loss_price:
                    should_exit = True
                    exit_reason = "Stop loss"
                elif position.side == TradeSide.SELL and current_price >= position.stop_loss_price:
                    should_exit = True
                    exit_reason = "Stop loss"
                
                if should_exit:
                    await self._execute_exit_signal(position, current_price, exit_reason, db)
                    
        except Exception as e:
            logger.error(f"Error checking exit signals: {e}")
    
    async def _execute_exit_signal(self, position: Position, exit_price: float, 
                                 reason: str, db: Session):
        """Execute exit signal by closing position"""
        try:
            # Create exit trade
            exit_side = TradeSide.SELL if position.side == TradeSide.BUY else TradeSide.BUY
            
            trade = Trade(
                user_id=position.user_id,
                client_order_id=str(uuid.uuid4()),
                symbol=position.symbol,
                exchange=position.exchange,
                side=exit_side,
                trade_type='market',
                status=TradeStatus.FILLED,
                quantity=position.quantity,
                price=exit_price,
                average_fill_price=exit_price,
                filled_quantity=position.quantity,
                turtle_system=position.turtle_system,
                filled_at=datetime.utcnow(),
                notes=f"Exit: {reason}"
            )
            
            # Calculate P&L
            if position.side == TradeSide.BUY:
                pnl = (exit_price - position.average_entry_price) * position.quantity
            else:
                pnl = (position.average_entry_price - exit_price) * position.quantity
            
            trade.realized_pnl = pnl
            position.realized_pnl = pnl
            
            # Close position
            position.is_open = False
            position.closed_at = datetime.utcnow()
            
            # Update last trade profitability for System 1 filter
            if position.turtle_system == TurtleSystem.SYSTEM_1:
                last_trade_key = f"{position.user_id}:{position.symbol}:system_1"
                self.last_trade_profitable[last_trade_key] = pnl > 0
            
            db.add(trade)
            db.commit()
            
            # Update portfolio
            await portfolio_manager.update_portfolio_after_trade(trade, db)
            
            logger.info(f"Position closed: {position.symbol} P&L: {pnl:.2f} Reason: {reason}")
            
        except Exception as e:
            logger.error(f"Error executing exit signal: {e}")
            db.rollback()
    
    async def _check_pyramid_signals(self, user: User, symbol: str, db: Session):
        """Check for pyramid (unit addition) opportunities"""
        try:
            positions = db.query(Position).filter(
                Position.user_id == user.id,
                Position.symbol == symbol,
                Position.is_open == True,
                Position.total_units < Position.max_units
            ).all()
            
            for position in positions:
                # Get current price
                price_data = await market_data_service.get_current_price(symbol)
                current_price = price_data['price']
                
                # Get ATR for pyramid calculation
                historical_data = await market_data_service.get_historical_data(symbol)
                atr = TechnicalIndicators.average_true_range(
                    historical_data['high'], 
                    historical_data['low'], 
                    historical_data['close']
                ).iloc[-1]
                
                # Check pyramid signal
                pyramid_signal = TechnicalIndicators.pyramid_signals(
                    current_price,
                    position.last_pyramid_price or position.average_entry_price,
                    atr,
                    position.side.value,
                    position.total_units,
                    position.max_units
                )
                
                if pyramid_signal['can_add']:
                    await self._execute_pyramid_signal(position, current_price, atr, db)
                    
        except Exception as e:
            logger.error(f"Error checking pyramid signals: {e}")
    
    async def _execute_pyramid_signal(self, position: Position, current_price: float, 
                                    atr: float, db: Session):
        """Execute pyramid signal by adding units"""
        try:
            # Calculate additional position size (same as original unit)
            portfolio = await portfolio_manager.get_portfolio(position.user_id, db)
            
            position_calc = TechnicalIndicators.calculate_position_size(
                portfolio.total_equity,
                current_price,
                atr,
                portfolio.risk_per_trade
            )
            
            # Risk management check
            risk_check = await risk_manager.validate_trade(
                position.user_id, position.symbol, position.side, 
                position_calc['position_size'], current_price, db
            )
            
            if not risk_check['approved']:
                return
            
            # Create pyramid trade
            trade = Trade(
                user_id=position.user_id,
                client_order_id=str(uuid.uuid4()),
                symbol=position.symbol,
                exchange=position.exchange,
                side=position.side,
                trade_type='market',
                status=TradeStatus.FILLED,
                quantity=position_calc['position_size'],
                price=current_price,
                average_fill_price=current_price,
                filled_quantity=position_calc['position_size'],
                turtle_system=position.turtle_system,
                unit_number=position.total_units + 1,
                filled_at=datetime.utcnow(),
                notes="Pyramid addition"
            )
            
            db.add(trade)
            
            # Update position
            await self._create_or_update_position(trade, db)
            
            db.commit()
            
            logger.info(f"Pyramid executed: {position.symbol} Unit {position.total_units + 1}")
            
        except Exception as e:
            logger.error(f"Error executing pyramid signal: {e}")
            db.rollback()


# Global trading engine instance
trading_engine = TradingEngine()
